using UnityEngine;
using System.Collections;

public class OtherVehicleEnterexit : MonoBehaviour
{
    [Header("Vehicle Tags")]
    public string helicopterTag = "Helicopter";
    public string airplaneTag = "Airplane";
    public string motorbikeTag = "Motorbike";
    public string boatTag = "Boat";
    public string trainTag = "Train";
    [Header("Position")]
    public Transform BoatPosition;
    [Header("Cameras")]
    public GameObject motorbikecam, helicoptercam, Playercam, airplanecam, traincam, boatcam;

    [Header("UI Canvas")]
    public GameObject helicoptercanvas, airplanecanvas, motorbikecanvas, boatcanvas, trainscanvas, Playercanvas;

    [Header("Player")]
    public Transform Player;

    [Header("Settings")]
    public float distance = 10f;
    public float motorbikeDistance = 4f; // Distance for motorbike
    public float maxExitSpeed = 5f; // Maximum speed to allow exit (in km/h)

    [Header("UI Buttons")]
    public GameObject Helicopterenterbutton, Helicopterexitbutton;
    public GameObject Airplaneenterbutton, Airplaneexitbutton;
    public GameObject Motorbikeenterbutton, Motorbikeexitbutton;
    public GameObject Boatenterbutton, Boatexitbutton;
    public GameObject Trainenterbutton, Trainexitbutton;

    // Private variables to track state
    private bool isInVehicle = false;
    private GameObject currentVehicle = null;
    private Transform originalParent;

    void Start()
    {
        // Hide all buttons at start
        HideAllButtons();
    }

    void Update()
    {
        if (!isInVehicle)
        {
            CheckVehicleDistances();
        }
        else
        {
            CheckExitConditions();
        }
    }

    private void HideAllButtons()
    {
        if (Helicopterenterbutton != null) Helicopterenterbutton.SetActive(false);
        if (Helicopterexitbutton != null) Helicopterexitbutton.SetActive(false);
        if (Airplaneenterbutton != null) Airplaneenterbutton.SetActive(false);
        if (Airplaneexitbutton != null) Airplaneexitbutton.SetActive(false);
        if (Motorbikeenterbutton != null) Motorbikeenterbutton.SetActive(false);
        if (Motorbikeexitbutton != null) Motorbikeexitbutton.SetActive(false);
        if (Boatenterbutton != null) Boatenterbutton.SetActive(false);
        if (Boatexitbutton != null) Boatexitbutton.SetActive(false);
        if (Trainenterbutton != null) Trainenterbutton.SetActive(false);
        if (Trainexitbutton != null) Trainexitbutton.SetActive(false);
    }

    private void CheckVehicleDistances()
    {
        // Check helicopter distance
        GameObject helicopter = GameObject.FindGameObjectWithTag(helicopterTag);
        if (helicopter != null && Vector3.Distance(Player.position, helicopter.transform.position) <= distance)
        {
            if (Helicopterenterbutton != null) Helicopterenterbutton.SetActive(true);
        }
        else
        {
            if (Helicopterenterbutton != null) Helicopterenterbutton.SetActive(false);
        }

        // Check airplane distance
        GameObject airplane = GameObject.FindGameObjectWithTag(airplaneTag);
        if (airplane != null && Vector3.Distance(Player.position, airplane.transform.position) <= distance)
        {
            if (Airplaneenterbutton != null) Airplaneenterbutton.SetActive(true);
        }
        else
        {
            if (Airplaneenterbutton != null) Airplaneenterbutton.SetActive(false);
        }

        // Check motorbike distance
        GameObject motorbike = GameObject.FindGameObjectWithTag(motorbikeTag);
        if (motorbike != null && Vector3.Distance(Player.position, motorbike.transform.position) <= motorbikeDistance)
        {
            if (Motorbikeenterbutton != null) Motorbikeenterbutton.SetActive(true);
        }
        else
        {
            if (Motorbikeenterbutton != null) Motorbikeenterbutton.SetActive(false);
        }

        // Check boat distance
        GameObject boat = GameObject.FindGameObjectWithTag(boatTag);
        if (boat != null && Vector3.Distance(Player.position, boat.transform.position) <= distance)
        {
            if (Boatenterbutton != null) Boatenterbutton.SetActive(true);
        }
        else
        {
            if (Boatenterbutton != null) Boatenterbutton.SetActive(false);
        }

        // Check train distance
        GameObject train = GameObject.FindGameObjectWithTag(trainTag);
        if (train != null && Vector3.Distance(Player.position, train.transform.position) <= distance)
        {
            if (Trainenterbutton != null) Trainenterbutton.SetActive(true);
        }
        else
        {
            if (Trainenterbutton != null) Trainenterbutton.SetActive(false);
        }
    }

    private void CheckExitConditions()
    {
        if (currentVehicle == null) return;

        float vehicleSpeed = 0f;

        // Get speed from rigidbody velocity (universal method)
        var vehicleRigidbody = currentVehicle.GetComponent<Rigidbody>();
        if (vehicleRigidbody != null)
        {
            // Convert m/s to km/h
            vehicleSpeed = vehicleRigidbody.linearVelocity.magnitude * 3.6f;
        }

        // Show exit button based on vehicle type and speed
        if (currentVehicle.CompareTag(helicopterTag))
        {
            // Helicopter can exit anytime (no speed restriction)
            if (Helicopterexitbutton != null) Helicopterexitbutton.SetActive(true);
        }
        else if (currentVehicle.CompareTag(airplaneTag))
        {
            if (Airplaneexitbutton != null) Airplaneexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle.CompareTag(motorbikeTag))
        {
            if (Motorbikeexitbutton != null) Motorbikeexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle.CompareTag(boatTag))
        {
            if (Boatexitbutton != null) Boatexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle.CompareTag(trainTag))
        {
            if (Trainexitbutton != null) Trainexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
    }

    // Helicopter Enter/Exit Functions
    public void EnterHelicopter()
    {
        GameObject helicopter = GameObject.FindGameObjectWithTag(helicopterTag);
        if (helicopter != null)
            EnterVehicle(helicopter, helicoptercam, helicoptercanvas);
    }

    public void ExitHelicopter()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(helicopterTag))
            ExitVehicle(currentVehicle, helicoptercam, helicoptercanvas);
    }

    // Airplane Enter/Exit Functions
    public void EnterAirplane()
    {
        GameObject airplane = GameObject.FindGameObjectWithTag(airplaneTag);
        if (airplane != null)
            EnterVehicle(airplane, airplanecam, airplanecanvas);
    }

    public void ExitAirplane()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(airplaneTag))
            ExitVehicle(currentVehicle, airplanecam, airplanecanvas);
    }

    // Motorbike Enter/Exit Functions
    public void EnterMotorbike()
    {
        GameObject motorbike = GameObject.FindGameObjectWithTag(motorbikeTag);
        if (motorbike != null)
            EnterVehicle(motorbike, motorbikecam, motorbikecanvas);
    }

    public void ExitMotorbike()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(motorbikeTag))
            ExitVehicle(currentVehicle, motorbikecam, motorbikecanvas);
    }

    // Boat Enter/Exit Functions
    public void EnterBoat()
    {
        GameObject boat = GameObject.FindGameObjectWithTag(boatTag);
        if (boat != null)
            EnterVehicle(boat, boatcam, boatcanvas);
    }

    public void ExitBoat()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(boatTag))
            ExitVehicle(currentVehicle, boatcam, boatcanvas);
            tag = "Boat";
    }

    // Train Enter/Exit Functions
    public void EnterTrain()
    {
        GameObject train = GameObject.FindGameObjectWithTag(trainTag);
        if (train != null)
            EnterVehicle(train, traincam, trainscanvas);
    }

    public void ExitTrain()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(trainTag))
            ExitVehicle(currentVehicle, traincam, trainscanvas);
    }

    // Generic Enter Vehicle Function
    private void EnterVehicle(GameObject vehicle, GameObject vehicleCamera, GameObject vehicleCanvas)
    {
        if (vehicle == null) return;

        // Store original parent before parenting to vehicle
        originalParent = Player.parent;
        currentVehicle = vehicle;

        // Switch cameras and UI
        if (Playercam != null) Playercam.SetActive(false);
        if (Player != null) Player.gameObject.SetActive(false);
        if (vehicleCamera != null) vehicleCamera.SetActive(true);
        if (Playercanvas != null) Playercanvas.SetActive(false);
        if (vehicleCanvas != null) vehicleCanvas.SetActive(true);

        // Enable vehicle engine based on vehicle type
        EnableVehicleEngine(vehicle, true);

        // Enable any vehicle controller (generic approach)
        var vehicleController = vehicle.GetComponent<MonoBehaviour>();
        if (vehicleController != null)
        {
            vehicleController.enabled = true;
        }


        // Parent player to vehicle and set position
        if (Player != null)
        {
            Player.SetParent(vehicle.transform);
            var playerPosition = vehicle.GetComponent<VehiclePlayerPosition>();
            if (playerPosition != null && playerPosition.SeatPosition != null)
            {
                Player.position = playerPosition.SeatPosition.position;
            }
        }

        // Hide all enter buttons and set vehicle state
        HideAllButtons();
        isInVehicle = true;
    }

    // Generic Exit Vehicle Function
    private void ExitVehicle(GameObject vehicle, GameObject vehicleCamera, GameObject vehicleCanvas)
    {
        if (vehicle == null || !isInVehicle) return;

        // Unparent player from vehicle first
        if (Player != null) Player.SetParent(originalParent);

        // Switch cameras and UI
        if (Playercam != null) Playercam.SetActive(true);
        if (Player != null) Player.gameObject.SetActive(true);
        if (vehicleCamera != null) vehicleCamera.SetActive(false);
        if (Playercanvas != null) Playercanvas.SetActive(true);
        if (vehicleCanvas != null) vehicleCanvas.SetActive(false);

        // Set player position to door position (now unparented, so it's relative to world)
        var playerPosition = vehicle.GetComponent<VehiclePlayerPosition>();
        if (playerPosition != null && playerPosition.DoorPosition != null && Player != null)
        {
            Player.position = playerPosition.DoorPosition.position;
        }

        // Disable vehicle engine
        EnableVehicleEngine(vehicle, false);

        // Handle vehicle rigidbody for stopping
       

        // Disable vehicle controller after delay
        StartCoroutine(DisableVehicleAfterDelay(vehicle));

        // Reset state
        currentVehicle = null;
        isInVehicle = false;
        HideAllButtons();
    }

    private IEnumerator DisableVehicleAfterDelay(GameObject vehicle)
    {
        yield return new WaitForSeconds(1f);

        // Disable any vehicle controller components
        var vehicleController = vehicle.GetComponent<MonoBehaviour>();
        if (vehicleController != null)
        {
            vehicleController.enabled = false;
        }
    }

    // Enable/Disable vehicle engines using reflection to avoid type dependencies
    private void EnableVehicleEngine(GameObject vehicle, bool enable)
    {
        // Get all MonoBehaviour components on the vehicle
        var components = vehicle.GetComponents<MonoBehaviour>();

        foreach (var component in components)
        {
            if (component == null) continue;

            var componentType = component.GetType();
            var componentName = componentType.Name;

            // Handle different vehicle controllers by name
            switch (componentName)
            {
                case "HelicopterController":
                    // Set EngineForce property
                    var engineForceProperty = componentType.GetProperty("EngineForce");
                    if (engineForceProperty != null)
                    {
                        engineForceProperty.SetValue(component, enable ? 10f : 0f);
                    }
                    break;

                case "TrainController_v3":
                    // Set EnginesOn property
                    var enginesOnProperty = componentType.GetProperty("EnginesOn");
                    if (enginesOnProperty != null)
                    {
                        enginesOnProperty.SetValue(component, enable);
                    }
                    break;

                case "BikeControl":
                    // Enable/disable the component and control engine
                    component.enabled = enable;
                    if (enable)
                    {
                        // Start the bike engine when entering
                        var startEngineMethod = componentType.GetMethod("StartEngine");
                        if (startEngineMethod != null)
                        {
                            startEngineMethod.Invoke(component, null);
                        }
                    }
                    else
                    {
                        // Stop the bike engine when exiting
                        var stopEngineMethod = componentType.GetMethod("StopEngine");
                        if (stopEngineMethod != null)
                        {
                            stopEngineMethod.Invoke(component, null);
                        }
                    }
                    break;

                case "BoatController":
                    // Enable/disable the component
                    component.enabled = enable;
                    break;
            }
        }
    }
}
